<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>openai-proxy – Purpose & Key Features</title>
  <style>
    /* Google Fonts Import */
    @import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700;900&family=Noto+Sans+SC:wght@400;700&display=swap');

    /* Keyframes for animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(25px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Base and Body Styles */
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100vw;
      height: 100vh;
      background-color: #333; /* Neutral background for the viewport */
      font-family: 'Lato', 'Noto Sans SC', sans-serif;
    }

    /* Slide Container */
    .slide {
      width: 1280px;
      height: 720px;
      background-color: #FDFCF9; /* Warm off-white */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 60px 80px;
      box-sizing: border-box;
      box-shadow: 0 15px 30px rgba(0,0,0,0.15), 0 8px 10px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    /* Content Wrapper for Animation */
    .slide-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      opacity: 0;
      animation: fadeInUp 0.8s 0.2s ease-out forwards;
    }
    
    /* Title */
    .title {
      font-size: 50px;
      font-weight: 900;
      color: #2D1810; /* Dark brown */
      text-align: center;
      margin: 0 0 25px 0;
    }

    /* Mission Statement */
    .mission {
      font-size: 26px;
      color: #E07B39; /* Warm orange accent */
      text-align: center;
      margin: 0 auto 60px auto;
      max-width: 85%;
      font-weight: 700;
      line-height: 1.4;
    }

    /* Features Grid Layout */
    .features-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto auto;
      gap: 30px 60px; /* row-gap column-gap */
      width: 100%;
      list-style-type: none;
      padding: 0;
      margin: 0;
    }

    /* Individual Feature Item */
    .feature-item {
      display: flex;
      align-items: flex-start;
      color: #5D4E42; /* Medium brown */
      font-size: 20px;
      line-height: 1.6;
      opacity: 0;
      animation: fadeInUp 0.6s ease-out forwards;
    }

    /* Staggered animation for list items */
    .feature-item:nth-child(1) { animation-delay: 0.5s; }
    .feature-item:nth-child(2) { animation-delay: 0.6s; }
    .feature-item:nth-child(3) { animation-delay: 0.7s; }
    .feature-item:nth-child(4) { animation-delay: 0.8s; }
    .feature-item:nth-child(5) { animation-delay: 0.9s; }
    .feature-item:nth-child(6) { animation-delay: 1.0s; }

    /* Custom Bullet Point Accent */
    .feature-item::before {
      content: '•';
      color: #E07B39; /* Warm orange */
      font-size: 32px;
      line-height: 1; /* Aligns with flex-start */
      margin-right: 15px;
      margin-top: 1px;
      flex-shrink: 0;
    }
    
    .feature-item > div {
        flex-grow: 1;
    }

    .feature-item .icon {
        margin-right: 8px;
        font-size: 22px;
        vertical-align: middle;
        display: inline-block;
        margin-top: -4px;
    }
    
    .feature-item strong {
        color: #2D1810; /* Dark brown for emphasis */
        font-weight: 700;
    }

  </style>
</head>
<body>
  <div class="slide">
    <div class="slide-content">
      <h1 class="title">openai-proxy – Purpose &amp; Key Features</h1>
      
      <p class="mission">
        FastAPI-based OpenAI-compatible proxy for local model API calls
      </p>

      <ul class="features-grid">
        <li class="feature-item">
          <div><span class="icon">🔌</span><strong>OpenAI API Compatibility:</strong> Full Chat Completions API v1 specification</div>
        </li>
        <li class="feature-item">
          <div><span class="icon">🏠</span><strong>Local Model Support:</strong> Connect to locally running model engines via BusTokenCompleter</div>
        </li>
        <li class="feature-item">
          <div><span class="icon">🛠️</span><strong>Tool Calling Support:</strong> OpenAI format function calling and tool calls</div>
        </li>
        <li class="feature-item">
          <div><span class="icon">🌊</span><strong>Streaming Response:</strong> Both streaming and non-streaming modes</div>
        </li>
        <li class="feature-item">
          <div><span class="icon">🎯</span><strong>Multi-Model Configuration:</strong> Dynamic `topic$renderer` format support</div>
        </li>
        <li class="feature-item">
          <div><span class="icon">⚡</span><strong>High-Performance Architecture:</strong> FastAPI + async processing for high concurrency</div>
        </li>
      </ul>
    </div>
  </div>
</body>
</html>