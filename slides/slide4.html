<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>openai-proxy – Architecture Diagram</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --bg-color: #FDFCF9;
      --title-color: #2D1810;
      --text-color: #5D4E42;
      --accent-color: #E07B39;
      --strong-text-color: #4A3F35;
    }

    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f0f0;
      font-family: 'Nunito Sans', 'Helvetica Neue', Arial, sans-serif;
    }

    .slide {
      width: 1280px;
      height: 720px;
      background-color: var(--bg-color);
      display: flex;
      flex-direction: column;
      padding: 60px;
      box-sizing: border-box;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(25px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .slide > * {
        animation: slideUp 0.7s ease-out backwards;
    }

    h1 {
      color: var(--title-color);
      font-size: 52px;
      font-weight: 700;
      margin: 0 0 35px 0;
      text-align: center;
      animation-delay: 0.1s;
    }

    .content-container {
      display: flex;
      flex: 1;
      gap: 40px;
      align-items: center;
    }

    .image-section {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .architecture-diagram {
      max-width: 100%;
      max-height: 450px;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(45, 24, 16, 0.1);
      animation-delay: 0.2s;
    }

    .text-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .layers-list {
      list-style: none;
      padding-left: 0;
      margin: 0;
      width: 100%;
      counter-reset: layer-counter;
    }

    .layers-list li {
      color: var(--text-color);
      font-size: 19px;
      line-height: 1.6;
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      animation: slideUp 0.7s ease-out backwards;
    }
    
    .layers-list li:last-child {
        margin-bottom: 0;
    }

    .layers-list li::before {
      content: counter(layer-counter);
      counter-increment: layer-counter;
      color: var(--accent-color);
      font-weight: 700;
      font-size: 20px;
      margin-right: 12px;
      min-width: 22px;
      text-align: right;
      padding-top: 2px;
    }

    .layers-list strong {
      color: var(--strong-text-color);
      font-weight: 600;
      margin-right: 8px;
    }

    .layers-list li:nth-child(1) { animation-delay: 0.4s; }
    .layers-list li:nth-child(2) { animation-delay: 0.5s; }
    .layers-list li:nth-child(3) { animation-delay: 0.6s; }
    .layers-list li:nth-child(4) { animation-delay: 0.7s; }
    .layers-list li:nth-child(5) { animation-delay: 0.8s; }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .content-container {
        flex-direction: column;
        gap: 30px;
      }

      .image-section, .text-section {
        flex: none;
        width: 100%;
      }

      .architecture-diagram {
        max-height: 300px;
      }

      .layers-list li {
        font-size: 18px;
      }
    }

    @media (max-width: 768px) {
      .slide {
        padding: 40px;
      }

      h1 {
        font-size: 42px;
        margin-bottom: 25px;
      }

      .content-container {
        gap: 20px;
      }

      .architecture-diagram {
        max-height: 250px;
      }

      .layers-list li {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .layers-list li::before {
        font-size: 18px;
        min-width: 20px;
        margin-right: 10px;
      }
    }

  </style>
</head>
<body>
  <div class="slide">
    <h1>openai-proxy – Architecture Diagram</h1>
    <div class="content-container">
      <div class="image-section">
        <img class="architecture-diagram" src="images/openai-proxy.png" alt="openai-proxy Architecture Diagram">
      </div>
      <div class="text-section">
        <ol class="layers-list">
 <li><strong>Client Layer:</strong> Corpnet(moonfire_genaicore), CES, Green User</li>
<li><strong>Auth Layer:</strong> Corp, MI, Green Tokens</li>
<li><strong>Access Layer:</strong> Research Endpoint, VPN + Ingress</li>
<li><strong>Deploy Layer:</strong> k8s pod (openai_proxy)</li>
<li><strong>Communication Layer:</strong> Redis-based communication with bus</li>
<li><strong>Engine Layer:</strong> Bus engine handling request</li>
        </ol>
      </div>
    </div>
  </div>
</body>
</html>