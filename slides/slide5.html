<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>openai-proxy - Usage Examples</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Source+Code+Pro:wght@400;500&display=swap');

    body {
      margin: 0;
      padding: 0;
      font-family: 'Inter', sans-serif;
      background-color: #f0f0f0;
    }

    .slide {
      width: 1280px;
      height: 720px;
      background-color: #FDFCF9;
      color: #5D4E42;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 60px 80px;
      position: relative; /* For absolute positioning of any future elements if needed */
      overflow: hidden; /* Ensures no content spills out */
    }

    h1 {
      font-size: 52px;
      color: #2D1810;
      text-align: center;
      margin: 0 0 50px 0;
      font-weight: 700;
    }

    .content-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 60px;
      width: 100%;
      flex-grow: 1; /* Allows content to take available space but not overflow */
      height: 0; /* Necessary for flex-grow to work properly in a flex-column */
    }

    .column {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    .column h2 {
      font-size: 26px;
      color: #5D4E42;
      margin-top: 0;
      margin-bottom: 20px;
      font-weight: 700;
    }

    .code-block {
      background-color: #FBF8F5;
      border: 1px solid #EAE0D9;
      border-radius: 12px;
      padding: 0px 20px;
      font-family: 'Source Code Pro', monospace;
      font-size: 14px;
      line-height: 1.5;
      white-space: pre-wrap;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      overflow-x: auto;
    }
    
    .code-block code .keyword { color: #E07B39; }
    .code-block code .string { color: #5D4E42; }
    .code-block code .comment { color: #9E9086; }
    .code-block code .class-name { color: #2D1810; }
    .code-block code .function { color: #8C6A5A; }
    .code-block code .number { color: #8C6A5A; }
    .code-block code .builtin { color: #E07B39; }
    .code-block code .operator { color: #2D1810; }

    .info-block {
      background-color: #FBF8F5;
      border: 1px solid #EAE0D9;
      border-radius: 12px;
      padding: 16px 20px;
      font-family: 'Source Code Pro', monospace;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 25px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      word-break: break-all;
    }

    .info-block.config {
      color: #E07B39;
    }

    .info-block.example {
        color: #5D4E42;
        font-size: 14px;
        line-height: 1.5;
    }

    ul {
      list-style: none;
      padding-left: 0;
      margin: 0;
    }

    ul li {
      font-size: 18px;
      line-height: 1.7;
      margin-bottom: 10px;
      display: flex;
      align-items: flex-start;
    }

    ul li::before {
      content: '•';
      color: #E07B39;
      font-size: 24px;
      margin-right: 12px;
      line-height: 0.9;
    }

    ul li strong {
      color: #2D1810;
      font-weight: 700;
      margin-right: 8px;
    }

  </style>
</head>
<body>
  <div class="slide">
    <h1>openai-proxy – Usage Examples</h1>
    <div class="content-wrapper">
      <div class="column">
        <h2>Base Example</h2>
        <div class="code-block">
          <code>
<span class="keyword">import</span> <span class="builtin">os</span>
<span class="keyword">import</span> <span class="builtin">requests</span>


<span class="comment"># Model name</span>
<span class="function">model</span> <span class="operator">=</span> <span class="string">&lt;topic$renderer&gt;</span>

<span class="comment"># API endpoint</span>
<span class="function">url</span> <span class="operator">=</span> <span class="string">"https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org/v1/chat/completions"</span>

<span class="comment"># Read cookie from environment variable, you can get the cookie via oai_cookie</span>
<span class="function">orange_cookie</span> <span class="operator">=</span> <span class="builtin">os</span><span class="operator">.</span><span class="function">getenv</span><span class="operator">(</span><span class="string">"ORANGE_API_COOKIE"</span><span class="operator">)</span>

<span class="function">headers</span> <span class="operator">=</span> <span class="operator">{</span>
    <span class="string">"Content-Type"</span><span class="operator">:</span> <span class="string">"application/json"</span><span class="operator">,</span>
    <span class="string">"Cookie"</span><span class="operator">:</span> <span class="function">orange_cookie</span><span class="operator">,</span>
<span class="operator">}</span>

<span class="function">payload</span> <span class="operator">=</span> <span class="operator">{</span>
    <span class="string">"model"</span><span class="operator">:</span> <span class="function">model</span><span class="operator">,</span>
    <span class="string">"messages"</span><span class="operator">:</span> <span class="operator">[</span>
        <span class="operator">{</span><span class="string">"role"</span><span class="operator">:</span> <span class="string">"system"</span><span class="operator">,</span> <span class="string">"content"</span><span class="operator">:</span> <span class="string">"You are an AI assistant."</span><span class="operator">}</span><span class="operator">,</span>
        <span class="operator">{</span><span class="string">"role"</span><span class="operator">:</span> <span class="string">"user"</span><span class="operator">,</span> <span class="string">"content"</span><span class="operator">:</span> <span class="string">"hi"</span><span class="operator">}</span><span class="operator">,</span>
    <span class="operator">]</span><span class="operator">,</span>
    <span class="string">"temperature"</span><span class="operator">:</span> <span class="number">0</span><span class="operator">,</span>
    <span class="string">"top_p"</span><span class="operator">:</span> <span class="number">0.95</span><span class="operator">,</span>
    <span class="string">"frequency_penalty"</span><span class="operator">:</span> <span class="number">0</span><span class="operator">,</span>
    <span class="string">"presence_penalty"</span><span class="operator">:</span> <span class="number">0</span><span class="operator">,</span>
    <span class="string">"parallel_tool_calls"</span><span class="operator">:</span> <span class="keyword">False</span><span class="operator">,</span>
<span class="operator">}</span>

<span class="function">response</span> <span class="operator">=</span> <span class="builtin">requests</span><span class="operator">.</span><span class="function">post</span><span class="operator">(</span><span class="function">url</span><span class="operator">,</span> <span class="function">headers</span><span class="operator">=</span><span class="function">headers</span><span class="operator">,</span> <span class="function">json</span><span class="operator">=</span><span class="function">payload</span><span class="operator">,</span> <span class="function">verify</span><span class="operator">=</span><span class="keyword">False</span><span class="operator">)</span>

<span class="builtin">print</span><span class="operator">(</span><span class="string">"Status:"</span><span class="operator">,</span> <span class="function">response</span><span class="operator">.</span><span class="function">status_code</span><span class="operator">)</span>
<span class="builtin">print</span><span class="operator">(</span><span class="string">"Response:"</span><span class="operator">,</span> <span class="function">response</span><span class="operator">.</span><span class="function">json</span><span class="operator">(</span><span class="operator">)</span><span class="operator">)</span>
          </code>
        </div>
      </div>
      <div class="column">
        <h2>Configuration Format</h2>
        <div class="info-block config">
          topic$renderer
        </div>
        <ul>
          <li><strong>topic:</strong> Model bus topic</li>
          <li><strong>renderer:</strong> Message format conversion</li>
          <li><strong>cookie:</strong> easy via oai_cookie tool</li>
        </ul>
        <h2 style="margin-top: 25px;">Example</h2>
        <div class="info-block example">
          bus:snap:orngcresco/twapi/mini/.../policy/step_000300:user:xuga$harmony_v4.0.16
        </div>
      </div>
    </div>
  </div>
</body>
</html>